// 云开发配置
const cloudbase = require('@cloudbase/node-sdk')
const path = require('path')
require('dotenv').config({ path: path.join(__dirname, '../../../.env') })

// 验证环境变量
const requiredEnvVars = [
  'CLOUDBASE_ENV',
  'CLOUDBASE_SECRET_ID', 
  'CLOUDBASE_SECRET_KEY'
]

function validateEnv() {
  const missing = requiredEnvVars.filter(key => !process.env[key])
  
  if (missing.length > 0) {
    console.error('❌ 缺少必要的环境变量:', missing.join(', '))
    console.error('请检查.env文件配置')
    process.exit(1)
  }
  
  console.log('✅ 环境变量验证通过')
}

// 验证密钥格式
function validateCredentials() {
  const { CLOUDBASE_SECRET_ID, CLOUDBASE_SECRET_KEY } = process.env
  
  if (!CLOUDBASE_SECRET_ID || !CLOUDBASE_SECRET_ID.startsWith('AKID')) {
    throw new Error('无效的SecretId格式')
  }
  
  if (!CLOUDBASE_SECRET_KEY || CLOUDBASE_SECRET_KEY.length < 20) {
    throw new Error('无效的SecretKey格式')
  }
  
  console.log('✅ 密钥格式验证通过')
}

// 初始化云开发
function initCloudBase() {
  try {
    validateEnv()
    validateCredentials()
    
    const app = cloudbase.init({
      env: process.env.CLOUDBASE_ENV,
      secretId: process.env.CLOUDBASE_SECRET_ID,
      secretKey: process.env.CLOUDBASE_SECRET_KEY
    })
    
    console.log('✅ 云开发SDK初始化成功')
    console.log('   环境ID:', process.env.CLOUDBASE_ENV)
    
    return app
    
  } catch (error) {
    console.error('❌ 云开发初始化失败:', error.message)
    process.exit(1)
  }
}

const app = initCloudBase()
const db = app.database()

module.exports = { app, db }
