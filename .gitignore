# 通用忽略文件
*.log
*.tmp
*.temp
.DS_Store
Thumbs.db

# 环境配置文件
.env
.env.local
.env.development
.env.production
*.env

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 依赖目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
*.tgz
*.tar.gz

# 微信小程序相关
# 保留project.config.json，但忽略私人配置
project.private.config.json
.miniprogram/

# 云开发相关
cloudfunctions/*/node_modules/
cloudfunctions/*/.env

# 管理后台相关
k12-admin/backend/.env
k12-admin/frontend/dist/
k12-admin/backend/logs/
k12-admin/backend/uploads/

# 开发工具相关
k12-dev-tool/logs/
k12-dev-tool/temp/
k12-dev-tool/output/

# 测试相关
coverage/
*.coverage
.nyc_output/

# 系统文件
*.pid
*.seed
*.pid.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 可选的REPL历史
.node_repl_history

# 输出的npm包
*.tgz

# Yarn完整性文件
.yarn-integrity

# dotenv环境变量文件
.env

# parcel-bundler缓存
.cache
.parcel-cache

# next.js构建输出
.next

# nuxt.js构建输出
.nuxt

# vuepress构建输出
.vuepress/dist

# Serverless目录
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地
.dynamodb/
