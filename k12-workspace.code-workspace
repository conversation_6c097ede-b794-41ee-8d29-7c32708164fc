{"folders": [{"name": "🏠 项目根目录", "path": "."}, {"name": "📱 K12小程序", "path": "./k12-wx"}, {"name": "🖥️ 管理后台", "path": "./k12-admin"}, {"name": "🔧 开发工具", "path": "./k12-dev-tool"}, {"name": "🎨 前端界面", "path": "./k12-admin/frontend"}, {"name": "⚙️ 后端服务", "path": "./k12-admin/backend"}], "settings": {"editor.tabSize": 2, "editor.insertSpaces": true, "editor.detectIndentation": false, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": true}, "eslint.workingDirectories": ["./k12-admin/frontend", "./k12-admin/backend", "./k12-dev-tool"], "typescript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifier": "relative", "emmet.includeLanguages": {"wxml": "html", "wxss": "css"}, "files.associations": {"*.wxml": "html", "*.wxss": "css", "*.wxs": "javascript"}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/logs": true, "**/temp": true, "**/uploads": true}, "files.exclude": {"**/node_modules": true, "**/.git": true, "**/dist": true, "**/build": true}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[markdown]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}, "extensions": {"recommendations": ["ms-vscode.vscode-json", "esbenp.prettier-vscode", "dbaeumer.vscode-eslint", "vue.volar", "vue.vscode-typescript-vue-plugin", "bradlc.vscode-tailwindcss", "ms-vscode.vscode-typescript-next", "formulahendry.auto-rename-tag", "christian-kohler.path-intellisense", "ms-vscode.vscode-json", "redhat.vscode-yaml", "ms-vscode.vscode-markdown", "yzhang.markdown-all-in-one"]}, "tasks": {"version": "2.0.0", "tasks": [{"label": "安装所有依赖", "type": "shell", "command": "npm run install:all", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "启动管理后台前端", "type": "shell", "command": "npm run dev:admin-frontend", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "启动管理后台后端", "type": "shell", "command": "npm run dev:admin-backend", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "运行所有测试", "type": "shell", "command": "npm run test:all", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "清理所有项目", "type": "shell", "command": "npm run clean", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}, "launch": {"version": "0.2.0", "configurations": [{"name": "调试后端服务", "type": "node", "request": "launch", "program": "${workspaceFolder}/k12-admin/backend/src/app.js", "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "restart": true, "runtimeExecutable": "nodemon", "skipFiles": ["<node_internals>/**"]}, {"name": "调试开发工具脚本", "type": "node", "request": "launch", "program": "${file}", "cwd": "${workspaceFolder}/k12-dev-tool", "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}}