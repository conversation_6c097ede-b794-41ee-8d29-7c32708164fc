// 系统配置服务
const { db } = require('../config/cloudbase')

class ConfigService {
  // 获取所有配置
  async getAllConfigs() {
    try {
      const result = await db.collection('system_configs').get()
      
      // 按分类组织配置
      const configsByCategory = {}
      result.data.forEach(config => {
        if (!configsByCategory[config.category]) {
          configsByCategory[config.category] = []
        }
        
        // 转换配置值类型
        let value = config.value
        switch (config.type) {
          case 'number':
            value = parseInt(value)
            break
          case 'boolean':
            value = value === 'true'
            break
          case 'json':
            try {
              value = JSON.parse(value)
            } catch (e) {
              console.warn(`JSON解析失败: ${config.key}`, e)
            }
            break
        }
        
        configsByCategory[config.category].push({
          ...config,
          value
        })
      })
      
      return {
        success: true,
        data: configsByCategory,
        total: result.data.length
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      throw new Error(`获取配置失败: ${error.message}`)
    }
  }

  // 获取单个配置
  async getConfig(key) {
    try {
      const result = await db.collection('system_configs')
        .where({ key })
        .get()
      
      if (result.data.length === 0) {
        return {
          success: false,
          error: '配置不存在'
        }
      }
      
      const config = result.data[0]
      let value = config.value
      
      // 转换类型
      switch (config.type) {
        case 'number':
          value = parseInt(value)
          break
        case 'boolean':
          value = value === 'true'
          break
        case 'json':
          try {
            value = JSON.parse(value)
          } catch (e) {
            console.warn(`JSON解析失败: ${key}`, e)
          }
          break
      }
      
      return {
        success: true,
        data: { ...config, value }
      }
    } catch (error) {
      console.error('获取配置失败:', error)
      throw new Error(`获取配置失败: ${error.message}`)
    }
  }

  // 更新配置
  async updateConfig(key, value, type = 'string') {
    try {
      // 转换值为字符串存储
      let stringValue = value
      if (type === 'json') {
        stringValue = JSON.stringify(value)
      } else if (type === 'boolean') {
        stringValue = value.toString()
      } else if (type === 'number') {
        stringValue = value.toString()
      }
      
      const result = await db.collection('system_configs')
        .where({ key })
        .update({
          data: {
            value: stringValue,
            type,
            updated_time: new Date()
          }
        })
      
      if (result.updated === 0) {
        return {
          success: false,
          error: '配置不存在或更新失败'
        }
      }
      
      return {
        success: true,
        message: '配置更新成功',
        updated: result.updated
      }
    } catch (error) {
      console.error('更新配置失败:', error)
      throw new Error(`更新配置失败: ${error.message}`)
    }
  }

  // 批量更新配置
  async batchUpdateConfigs(configs) {
    try {
      const results = []
      
      for (const config of configs) {
        const result = await this.updateConfig(
          config.key, 
          config.value, 
          config.type
        )
        results.push({
          key: config.key,
          ...result
        })
      }
      
      const successCount = results.filter(r => r.success).length
      const failCount = results.length - successCount
      
      return {
        success: failCount === 0,
        message: `批量更新完成: 成功${successCount}个, 失败${failCount}个`,
        results,
        successCount,
        failCount
      }
    } catch (error) {
      console.error('批量更新配置失败:', error)
      throw new Error(`批量更新配置失败: ${error.message}`)
    }
  }

  // 创建新配置
  async createConfig(configData) {
    try {
      const { key, value, type = 'string', category = 'general', description = '' } = configData
      
      // 检查是否已存在
      const existing = await this.getConfig(key)
      if (existing.success) {
        return {
          success: false,
          error: '配置已存在'
        }
      }
      
      // 转换值为字符串
      let stringValue = value
      if (type === 'json') {
        stringValue = JSON.stringify(value)
      } else if (type === 'boolean') {
        stringValue = value.toString()
      } else if (type === 'number') {
        stringValue = value.toString()
      }
      
      const result = await db.collection('system_configs').add({
        data: {
          key,
          value: stringValue,
          type,
          category,
          description,
          created_time: new Date()
        }
      })
      
      return {
        success: true,
        message: '配置创建成功',
        id: result.id
      }
    } catch (error) {
      console.error('创建配置失败:', error)
      throw new Error(`创建配置失败: ${error.message}`)
    }
  }

  // 删除配置
  async deleteConfig(key) {
    try {
      const result = await db.collection('system_configs')
        .where({ key })
        .remove()
      
      if (result.deleted === 0) {
        return {
          success: false,
          error: '配置不存在'
        }
      }
      
      return {
        success: true,
        message: '配置删除成功',
        deleted: result.deleted
      }
    } catch (error) {
      console.error('删除配置失败:', error)
      throw new Error(`删除配置失败: ${error.message}`)
    }
  }
}

module.exports = new ConfigService()
