{
  "compileOnSave": true,
  "compilerOptions": {
    "allowJs": false,
    "allowUnreachableCode": false,
    "allowUnusedLabels": false,
    "charset": "utf8",
    "declaration": true,
    "experimentalDecorators": true,
    "emitDecoratorMetadata": true,
    "importHelpers": false,
    "module": "commonjs",
    // "declarationDir": "types",
    "noEmitOnError": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "noUnusedParameters": false,
    "outDir": "dist/commonjs",
    "pretty": true,
    "removeComments": true,
    "stripInternal": true,
    "skipDefaultLibCheck": true,
    "skipLibCheck": true,
    "target": "es2017",
    "lib": ["es2015", "es6", "dom"]
  },
  "include": ["src/**/*"],
  "exclude": ["node_modules", "test"]
}
