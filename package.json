{"name": "k12-education-platform", "version": "1.0.0", "description": "K12教育资源管理平台 - 包含微信小程序、管理后台和开发工具", "private": true, "workspaces": ["k12-admin/frontend", "k12-admin/backend", "k12-admin"], "scripts": {"install:all": "npm run install:admin && npm run install:admin-frontend && npm run install:admin-backend", "install:admin": "cd k12-admin && npm install", "install:admin-frontend": "cd k12-admin/frontend && npm install", "install:admin-backend": "cd k12-admin/backend && npm install", "dev:admin-frontend": "cd k12-admin/frontend && npm run dev", "dev:admin-backend": "cd k12-admin/backend && npm run dev", "build:admin-frontend": "cd k12-admin/frontend && npm run build", "start:admin-backend": "cd k12-admin/backend && npm start", "test:all": "npm run test:admin-backend && npm run test:dev-tools", "test:admin-backend": "cd k12-admin/backend && npm test", "test:dev-tools": "cd k12-dev-tool && npm test", "lint": "echo 'Linting all projects...'", "clean": "npm run clean:admin && npm run clean:dev-tools", "clean:admin": "cd k12-admin && rm -rf node_modules frontend/node_modules backend/node_modules frontend/dist", "clean:dev-tools": "cd k12-dev-tool && rm -rf node_modules logs temp output"}, "keywords": ["k12", "education", "miniprogram", "admin", "vue", "nodejs", "cloudbase"], "author": "K12 Education Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/k12-education-platform.git"}, "bugs": {"url": "https://github.com/your-username/k12-education-platform/issues"}, "homepage": "https://github.com/your-username/k12-education-platform#readme"}