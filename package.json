{"name": "k12-admin", "version": "1.0.0", "description": "K12教育资源管理后台 - 前后端分离架构", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install", "dev:frontend": "npm run dev --workspace=frontend", "dev:backend": "npm run dev --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "start:backend": "npm run start --workspace=backend", "test:backend": "npm run test --workspace=backend", "clean": "rm -rf node_modules frontend/node_modules backend/node_modules", "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\""}, "keywords": ["k12", "education", "admin", "vue", "nodejs", "cloudbase"], "author": "K12 Admin Team", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}