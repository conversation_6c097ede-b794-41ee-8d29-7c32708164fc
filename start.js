#!/usr/bin/env node

/**
 * K12教育资源平台 - 项目启动脚本
 * 用于快速启动和管理整个项目的各个组件
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(50), 'cyan');
  log(message, 'bright');
  log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// 检查目录是否存在
function checkDirectory(dir) {
  return fs.existsSync(dir);
}

// 检查package.json是否存在
function checkPackageJson(dir) {
  return fs.existsSync(path.join(dir, 'package.json'));
}

// 运行命令
function runCommand(command, args, cwd, description) {
  return new Promise((resolve, reject) => {
    logInfo(`正在执行: ${description}`);
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        logSuccess(`完成: ${description}`);
        resolve();
      } else {
        logError(`失败: ${description} (退出码: ${code})`);
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      logError(`错误: ${description} - ${error.message}`);
      reject(error);
    });
  });
}

// 主要功能函数
async function installDependencies() {
  logHeader('安装项目依赖');

  // 安装根目录依赖
  if (checkPackageJson('.')) {
    await runCommand('npm', ['install'], '.', '安装根目录依赖');
  }

  // 安装管理后台依赖
  if (checkDirectory('k12-admin')) {
    if (checkPackageJson('k12-admin')) {
      await runCommand('npm', ['install'], 'k12-admin', '安装管理后台根依赖');
    }
    
    if (checkPackageJson('k12-admin/frontend')) {
      await runCommand('npm', ['install'], 'k12-admin/frontend', '安装前端依赖');
    }
    
    if (checkPackageJson('k12-admin/backend')) {
      await runCommand('npm', ['install'], 'k12-admin/backend', '安装后端依赖');
    }
  }

  // 安装开发工具依赖
  if (checkPackageJson('k12-dev-tool')) {
    await runCommand('npm', ['install'], 'k12-dev-tool', '安装开发工具依赖');
  }

  logSuccess('所有依赖安装完成！');
}

async function startAdminFrontend() {
  logHeader('启动管理后台前端');
  
  if (!checkDirectory('k12-admin/frontend')) {
    logError('管理后台前端目录不存在');
    return;
  }

  await runCommand('npm', ['run', 'dev'], 'k12-admin/frontend', '启动前端开发服务器');
}

async function startAdminBackend() {
  logHeader('启动管理后台后端');
  
  if (!checkDirectory('k12-admin/backend')) {
    logError('管理后台后端目录不存在');
    return;
  }

  // 检查环境配置文件
  const envFile = path.join('k12-admin/backend', '.env');
  if (!fs.existsSync(envFile)) {
    logWarning('后端环境配置文件 .env 不存在');
    logInfo('请复制 .env.example 为 .env 并配置相关参数');
  }

  await runCommand('npm', ['run', 'dev'], 'k12-admin/backend', '启动后端开发服务器');
}

async function runTests() {
  logHeader('运行项目测试');

  // 运行后端测试
  if (checkDirectory('k12-admin/backend')) {
    await runCommand('npm', ['test'], 'k12-admin/backend', '运行后端测试');
  }

  // 运行开发工具测试
  if (checkDirectory('k12-dev-tool')) {
    await runCommand('npm', ['test'], 'k12-dev-tool', '运行开发工具测试');
  }

  logSuccess('所有测试完成！');
}

function showHelp() {
  logHeader('K12教育资源平台 - 项目管理工具');
  
  log('\n使用方法:', 'bright');
  log('  node start.js [命令]', 'cyan');
  
  log('\n可用命令:', 'bright');
  log('  install     安装所有项目依赖', 'green');
  log('  frontend    启动管理后台前端', 'green');
  log('  backend     启动管理后台后端', 'green');
  log('  test        运行所有测试', 'green');
  log('  help        显示帮助信息', 'green');
  
  log('\n项目结构:', 'bright');
  log('  k12-wx/           微信小程序端', 'yellow');
  log('  k12-admin/        管理后台', 'yellow');
  log('  k12-dev-tool/     开发工具', 'yellow');
  
  log('\n快速开始:', 'bright');
  log('  1. node start.js install    # 安装依赖', 'cyan');
  log('  2. node start.js backend    # 启动后端', 'cyan');
  log('  3. node start.js frontend   # 启动前端', 'cyan');
  
  log('\n注意事项:', 'bright');
  log('  - 微信小程序需要使用微信开发者工具打开', 'magenta');
  log('  - 后端服务需要配置云开发环境变量', 'magenta');
  log('  - 前端默认运行在 http://localhost:5173', 'magenta');
  log('  - 后端默认运行在 http://localhost:3000', 'magenta');
}

// 主程序
async function main() {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'install':
        await installDependencies();
        break;
      case 'frontend':
        await startAdminFrontend();
        break;
      case 'backend':
        await startAdminBackend();
        break;
      case 'test':
        await runTests();
        break;
      case 'help':
      case undefined:
        showHelp();
        break;
      default:
        logError(`未知命令: ${command}`);
        showHelp();
        process.exit(1);
    }
  } catch (error) {
    logError(`执行失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行主程序
main();
