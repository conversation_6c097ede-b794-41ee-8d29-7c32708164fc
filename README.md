# K12教育资源管理后台

基于Vue 3和Node.js开发的K12教育资源管理后台系统，用于管理微信小程序的文件资源和系统配置。

## 📁 项目结构

```
k12-admin/
├── frontend/               # 前端界面
│   ├── src/
│   │   ├── components/     # Vue组件
│   │   ├── views/          # 页面视图
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # Pinia状态管理
│   │   └── utils/          # 工具函数
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                # 后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务逻辑
│   │   ├── middleware/     # 中间件
│   │   ├── routes/         # 路由定义
│   │   └── utils/          # 工具函数
│   └── package.json
├── package.json            # 根配置文件
└── 开发需求文档.md         # 详细需求文档
```

## 🚀 快速开始

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0
- 腾讯云开发账号和环境

### 1. 安装依赖
```bash
# 安装根目录依赖
npm install

# 安装前端依赖
cd frontend
npm install

# 安装后端依赖
cd ../backend
npm install
```

### 2. 环境配置
```bash
# 复制环境配置文件
cd backend
cp .env.example .env

# 编辑环境配置
# 填入云开发环境ID、密钥等信息
```

### 3. 启动服务
```bash
# 启动后端服务 (端口: 3000)
cd backend
npm run dev

# 启动前端服务 (端口: 5173)
cd ../frontend
npm run dev
```

### 4. 访问系统
- 前端界面: http://localhost:5173
- 后端API: http://localhost:3000

## 🔧 技术栈

### 前端技术
- **框架**: Vue 3 + Composition API
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios

### 后端技术
- **框架**: Express.js
- **云服务**: 腾讯云开发SDK
- **数据验证**: Joi
- **日志**: Winston
- **安全**: Helmet + CORS
- **限流**: Express Rate Limit

## 📋 主要功能

### 文件管理
- ✅ 批量文件上传（拖拽 + CSV）
- ✅ 文件列表管理和搜索
- ✅ 预览图自动生成
- ✅ 文件分类和标签管理
- ✅ 批量操作（启用/禁用/删除）

### 系统配置
- ✅ 广告配置管理
- ✅ 系统参数设置
- ✅ 热门搜索关键词
- ✅ 文件分类标准值

### 数据统计
- ✅ 文件统计分析
- ✅ 下载量排行
- ✅ 存储空间监控
- ✅ 用户反馈管理

### 数据管理
- ✅ 数据导出（Excel/JSON）
- ✅ 配置备份恢复
- ✅ 操作日志记录

## 🛠️ 开发指南

### 前端开发
```bash
cd frontend

# 开发模式
npm run dev

# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

### 后端开发
```bash
cd backend

# 开发模式（自动重启）
npm run dev

# 生产模式
npm start

# 运行测试
npm test
```

### API文档
后端API遵循RESTful设计规范：
- GET /api/files - 获取文件列表
- POST /api/files - 上传文件
- PUT /api/files/:id - 更新文件信息
- DELETE /api/files/:id - 删除文件

## 🔒 安全配置

### 环境变量配置
```env
# 云开发配置
CLOUDBASE_ENV_ID=your-env-id
CLOUDBASE_SECRET_ID=your-secret-id
CLOUDBASE_SECRET_KEY=your-secret-key

# 服务配置
PORT=3000
NODE_ENV=development

# 安全配置
JWT_SECRET=your-jwt-secret
UPLOAD_MAX_SIZE=52428800
```

### 安全特性
- 文件类型和大小验证
- 请求频率限制
- CORS跨域保护
- 安全头部设置
- 操作日志记录

## 📊 性能指标

- 单次批量上传: 支持50个文件
- 预览图生成: <30秒/文件
- CSV解析验证: <5秒
- 界面响应时间: <2秒

## 🔗 相关链接

- [详细需求文档](./开发需求文档.md)
- [K12小程序](../k12-wx/)
- [开发工具](../k12-dev-tool/)

## 📄 许可证

MIT License
