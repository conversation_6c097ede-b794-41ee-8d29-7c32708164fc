# K12教育资源平台

一个完整的K12教育资源管理平台，包含微信小程序端、管理后台和开发工具。

## 📁 项目结构

```
k12-project/
├── k12-wx/                 # 微信小程序端
│   ├── pages/              # 小程序页面
│   ├── components/         # 自定义组件
│   ├── cloudfunctions/     # 云函数
│   ├── utils/              # 工具函数
│   └── images/             # 图片资源
├── k12-admin/              # 管理后台
│   ├── frontend/           # 前端界面 (Vue 3)
│   └── backend/            # 后端服务 (Node.js)
└── k12-dev-tool/           # 开发工具&文档
    ├── docs/               # 项目文档
    ├── scripts/            # 调试脚本
    ├── tests/              # 测试用例
    └── tools/              # 开发工具
```

## 🚀 快速开始

### 1. 微信小程序端 (k12-wx)
```bash
cd k12-wx
# 使用微信开发者工具打开项目
# 配置云开发环境
```

### 2. 管理后台 (k12-admin)
```bash
cd k12-admin

# 启动后端服务
cd backend
npm install
npm run dev

# 启动前端界面
cd ../frontend
npm install
npm run dev
```

### 3. 开发工具 (k12-dev-tool)
```bash
cd k12-dev-tool
# 查看文档和使用工具脚本
```

## 🔧 技术栈

### 微信小程序端
- **框架**: 微信小程序原生框架
- **云服务**: 微信云开发
- **功能**: 文件浏览、下载、搜索、分类

### 管理后台
- **前端**: Vue 3 + Element Plus + Vite
- **后端**: Node.js + Express + 云开发SDK
- **功能**: 文件管理、系统配置、数据统计

### 开发工具
- **文档**: Markdown格式的开发文档
- **脚本**: Node.js调试和测试脚本
- **工具**: 数据生成、配置验证等

## 📋 主要功能

### 小程序端功能
- 📚 教育资源浏览和下载
- 🔍 智能搜索和分类筛选
- 📊 下载统计和热门推荐
- 💬 用户反馈和意见收集

### 管理后台功能
- 📁 批量文件上传和管理
- ⚙️ 系统配置和参数设置
- 📈 数据统计和分析报表
- 🛠️ 用户反馈处理

## 🔗 相关链接

- [小程序端详细说明](./k12-wx/README.md)
- [管理后台详细说明](./k12-admin/README.md)
- [开发工具详细说明](./k12-dev-tool/README.md)

## 📄 许可证

MIT License
