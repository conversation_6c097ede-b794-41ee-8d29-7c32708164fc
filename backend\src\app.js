// K12管理后台 - 后端应用
const express = require('express')
const cors = require('cors')
const helmet = require('helmet')
const compression = require('compression')
const rateLimit = require('express-rate-limit')
const path = require('path')
require('dotenv').config({ path: path.join(__dirname, '../../.env') })

const app = express()
const PORT = 8080

// 安全中间件
app.use(helmet({
  crossOriginEmbedderPolicy: false
}))

// CORS配置
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}))

// 限流中间件
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 1000, // 限制每个IP 1000次请求
  message: {
    success: false,
    error: 'API调用频率超限，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
})
app.use('/api/', limiter)

// 基础中间件
app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 请求日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} ${req.method} ${req.path}`)
  next()
})

// 路由
app.use('/api/config', require('./routes/config'))

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'K12管理后台服务运行正常',
    timestamp: new Date().toISOString(),
    env: process.env.CLOUDBASE_ENV
  })
})

// 根路径
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'K12教育资源管理后台 API',
    version: '1.0.0',
    endpoints: {
      config: '/api/config',
      health: '/health'
    }
  })
})

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: '接口不存在',
    path: req.originalUrl
  })
})

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('服务器错误:', error)
  res.status(500).json({
    success: false,
    error: '服务器内部错误',
    message: process.env.NODE_ENV === 'development' ? error.message : '请联系管理员'
  })
})

// 启动服务器
app.listen(PORT, () => {
  console.log('🚀 K12管理后台服务启动成功!')
  console.log(`   服务地址: http://localhost:${PORT}`)
  console.log(`   环境: ${process.env.NODE_ENV || 'development'}`)
  console.log(`   云开发环境: ${process.env.CLOUDBASE_ENV}`)
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━')
})

module.exports = app
