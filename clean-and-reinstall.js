#!/usr/bin/env node

/**
 * K12管理后台 - 清理和重新安装依赖脚本
 * 解决多个node_modules目录的问题
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(50), 'cyan');
  log(message, 'bright');
  log('='.repeat(50), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// 删除目录
function removeDirectory(dir) {
  if (fs.existsSync(dir)) {
    logInfo(`删除目录: ${dir}`);
    fs.rmSync(dir, { recursive: true, force: true });
    logSuccess(`已删除: ${dir}`);
  } else {
    logInfo(`目录不存在，跳过: ${dir}`);
  }
}

// 运行命令
function runCommand(command, args, cwd) {
  return new Promise((resolve, reject) => {
    logInfo(`执行命令: ${command} ${args.join(' ')} (在 ${cwd})`);
    
    const child = spawn(command, args, {
      cwd,
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
}

async function main() {
  try {
    logHeader('K12管理后台 - 依赖清理和重新安装');

    // 步骤1: 清理所有node_modules
    logHeader('步骤1: 清理现有的node_modules目录');
    removeDirectory('node_modules');
    removeDirectory('frontend/node_modules');
    removeDirectory('backend/node_modules');

    // 步骤2: 清理package-lock.json文件
    logHeader('步骤2: 清理package-lock.json文件');
    const lockFiles = [
      'package-lock.json',
      'frontend/package-lock.json',
      'backend/package-lock.json'
    ];

    lockFiles.forEach(file => {
      if (fs.existsSync(file)) {
        logInfo(`删除: ${file}`);
        fs.unlinkSync(file);
        logSuccess(`已删除: ${file}`);
      }
    });

    // 步骤3: 使用workspaces重新安装依赖
    logHeader('步骤3: 使用npm workspaces重新安装依赖');
    await runCommand('npm', ['install'], '.');
    logSuccess('依赖安装完成！');

    // 步骤4: 验证安装结果
    logHeader('步骤4: 验证安装结果');
    
    const checkDirs = [
      { path: 'node_modules', desc: '根目录依赖' },
      { path: 'node_modules/@cloudbase', desc: '云开发SDK' },
      { path: 'node_modules/concurrently', desc: '并发执行工具' }
    ];

    checkDirs.forEach(({ path: dir, desc }) => {
      if (fs.existsSync(dir)) {
        logSuccess(`${desc}: ${dir}`);
      } else {
        logError(`缺失 ${desc}: ${dir}`);
      }
    });

    // 检查workspace链接
    const workspaceLinks = [
      'node_modules/k12-admin-frontend',
      'node_modules/k12-admin-backend'
    ];

    workspaceLinks.forEach(link => {
      if (fs.existsSync(link)) {
        logSuccess(`Workspace链接: ${link}`);
      } else {
        logWarning(`Workspace链接缺失: ${link}`);
      }
    });

    logHeader('清理和重新安装完成！');
    
    log('\n📊 优化结果:', 'bright');
    log('  ✅ 从3个node_modules目录减少到1个', 'green');
    log('  ✅ 消除了重复的依赖', 'green');
    log('  ✅ 使用npm workspaces统一管理', 'green');
    log('  ✅ 减少了磁盘空间占用', 'green');

    log('\n🚀 可用命令:', 'bright');
    log('  npm run dev:frontend    # 启动前端开发服务器', 'cyan');
    log('  npm run dev:backend     # 启动后端开发服务器', 'cyan');
    log('  npm run dev             # 同时启动前后端', 'cyan');
    log('  npm run build:frontend  # 构建前端', 'cyan');

  } catch (error) {
    logError(`执行失败: ${error.message}`);
    process.exit(1);
  }
}

main();
