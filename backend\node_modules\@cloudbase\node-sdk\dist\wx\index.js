"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.wxCallContainerApi = exports.callWxPayApi = exports.callCompatibleWxOpenApi = exports.callWxOpenApi = void 0;
const tcbapicaller = __importStar(require("../utils/tcbapirequester"));
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
function validateCrossAccount(config, opts = {}) {
    const getCrossAccountInfo = opts.getCrossAccountInfo || config.getCrossAccountInfo;
    if (getCrossAccountInfo) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: 'invalid config: getCrossAccountInfo' }));
    }
}
async function callWxOpenApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {
    let transformRequestData;
    try {
        transformRequestData = requestData ? JSON.stringify(requestData) : '';
    }
    catch (e) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, e), { code: code_1.ERROR.INVALID_PARAM.code, message: '对象出现了循环引用' }));
    }
    validateCrossAccount(cloudbase.config, opts);
    const params = {
        action: 'wx.api',
        apiName,
        apiOptions,
        cgiName,
        requestData: transformRequestData
    };
    return await tcbapicaller.request({
        config: cloudbase.config,
        params,
        method: 'post',
        opts,
        headers: {
            'content-type': 'application/json'
        }
    }).then(res => {
        if (res.code) {
            return res;
        }
        let result;
        try {
            result = JSON.parse(res.data.responseData);
        }
        catch (e) {
            result = res.data.responseData;
        }
        return {
            result,
            requestId: res.requestId
        };
    });
}
exports.callWxOpenApi = callWxOpenApi;
/**
 * 调用wxopenAPi
 * @param {String} apiName  接口名
 * @param {Buffer} requestData
 * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}
 */
async function callCompatibleWxOpenApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {
    validateCrossAccount(cloudbase.config, opts);
    const params = {
        action: 'wx.openApi',
        apiName,
        apiOptions,
        cgiName,
        requestData
    };
    return await tcbapicaller.request({
        config: cloudbase.config,
        method: 'post',
        headers: { 'content-type': 'multipart/form-data' },
        params,
        isFormData: true,
        opts
    }).then(res => res);
}
exports.callCompatibleWxOpenApi = callCompatibleWxOpenApi;
/**
 * wx.wxPayApi 微信支付用
 * @param {String} apiName  接口名
 * @param {Buffer} requestData
 * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}
 */
async function callWxPayApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {
    validateCrossAccount(cloudbase.config, opts);
    const params = {
        action: 'wx.wxPayApi',
        apiName,
        apiOptions,
        cgiName,
        requestData
    };
    return await tcbapicaller.request({
        config: cloudbase.config,
        method: 'post',
        headers: { 'content-type': 'multipart/form-data' },
        params,
        isFormData: true,
        opts
    });
}
exports.callWxPayApi = callWxPayApi;
/**
 * wx.wxCallContainerApi
 * @param {String} apiName  接口名
 * @param {Buffer} requestData
 * @return {Promise} 正常内容为buffer，报错为json {code:'', message:'', resquestId:''}
 */
async function wxCallContainerApi(cloudbase, { apiName, apiOptions, cgiName, requestData }, opts) {
    validateCrossAccount(cloudbase.config, opts);
    const params = {
        action: 'wx.wxCallContainerApi',
        apiName,
        apiOptions,
        cgiName,
        requestData
    };
    return await tcbapicaller.request({
        config: cloudbase.config,
        method: 'post',
        headers: { 'content-type': 'multipart/form-data' },
        params,
        isFormData: true,
        opts
    });
}
exports.wxCallContainerApi = wxCallContainerApi;
