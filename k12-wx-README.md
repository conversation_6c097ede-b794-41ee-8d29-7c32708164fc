# K12教育资源小程序

基于微信小程序和云开发的K12教育资源下载平台，为学生和家长提供优质的教育资源。

## 📱 项目概述

这是一个专注于K12教育资源的微信小程序，提供各年级、各科目的教辅资料免费下载服务。

### 主要特色
- 🎯 **精准分类**: 按年级、科目、册别、板块精确分类
- 🔍 **智能搜索**: 支持关键词搜索和筛选
- 📊 **热门推荐**: 基于下载量的智能推荐
- 💬 **用户反馈**: 完善的意见反馈机制
- 📈 **数据统计**: 详细的下载和浏览统计

## 📁 项目结构

```
k12-wx/
├── pages/                  # 小程序页面
│   ├── index/              # 首页
│   ├── category/           # 分类页面
│   ├── search/             # 搜索页面
│   ├── files-list/         # 文件列表
│   ├── files-detail/       # 文件详情
│   ├── about/              # 关于页面
│   └── feedback/           # 反馈页面
├── components/             # 自定义组件
│   └── download-stats/     # 下载统计组件
├── cloudfunctions/         # 云函数
│   ├── getOpenId/          # 获取用户OpenID
│   ├── checkDownloadPermission/  # 检查下载权限
│   ├── recordDownload/     # 记录下载
│   └── updateViewCount/    # 更新浏览量
├── utils/                  # 工具函数
│   ├── api/                # API接口
│   ├── helpers/            # 辅助函数
│   ├── authManager.js      # 认证管理
│   ├── cloudApi.js         # 云开发API
│   ├── downloadManager.js  # 下载管理
│   └── userManager.js      # 用户管理
├── images/                 # 图片资源
│   ├── tabbar/             # 底部导航图标
│   └── files/              # 文件相关图标
├── app.js                  # 小程序入口文件
├── app.json                # 小程序配置
├── project.config.json     # 项目配置
└── sitemap.json           # 站点地图
```

## 🚀 快速开始

### 环境要求
- 微信开发者工具
- 微信小程序账号
- 腾讯云开发环境

### 1. 配置云开发
1. 在微信开发者工具中打开项目
2. 开通云开发服务
3. 配置云开发环境ID
4. 部署云函数

### 2. 数据库配置
创建以下集合：
- `files` - 文件资源集合
- `feedback` - 用户反馈集合
- `download_records` - 下载记录集合
- `system_config` - 系统配置集合

## 🔧 技术架构

### 前端技术
- **框架**: 微信小程序原生框架
- **样式**: WXSS + Flex布局
- **状态管理**: 全局数据管理
- **组件化**: 自定义组件开发

### 后端服务
- **云开发**: 微信云开发平台
- **数据库**: 云数据库（MongoDB）
- **存储**: 云存储服务
- **云函数**: Node.js运行时

## 📋 页面功能

### 首页 (index)
- 热门资源推荐
- 最新上传文件
- 快速分类入口
- 搜索功能入口

### 分类页面 (category)
- 年级分类浏览
- 科目分类浏览
- 册别和板块筛选

### 搜索页面 (search)
- 关键词搜索
- 高级筛选
- 搜索历史
- 热门搜索词

### 文件列表 (files-list)
- 分页加载
- 多维度排序
- 筛选功能
- 收藏功能

### 文件详情 (files-detail)
- 文件预览
- 详细信息展示
- 下载功能
- 相关推荐

## 📊 数据结构

### files集合
```javascript
{
  _id: "文件ID",
  title: "文件标题",
  description: "文件描述",
  grade: "年级",
  subject: "科目",
  volume: "册别",
  section: "板块",
  category: "分类",
  tags: ["标签1", "标签2"],
  fileUrl: "文件下载链接",
  previewUrl: "预览图链接",
  fileSize: 1024000,
  downloadCount: 100,
  viewCount: 500,
  createTime: "2024-01-01T00:00:00.000Z",
  updateTime: "2024-01-01T00:00:00.000Z",
  status: "active"
}
```

## 🔗 相关链接

- [管理后台](../k12-admin/)
- [开发工具](../k12-dev-tool/)
- [微信小程序官方文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)

## 📄 许可证

MIT License
