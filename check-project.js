#!/usr/bin/env node

/**
 * K12教育资源平台 - 项目状态检查脚本
 * 检查项目结构、依赖、配置等状态
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkExists(filePath, description) {
  const exists = fs.existsSync(filePath);
  const status = exists ? '✅' : '❌';
  const color = exists ? 'green' : 'red';
  log(`${status} ${description}`, color);
  return exists;
}

function checkGitRepo(dir, name) {
  const gitDir = path.join(dir, '.git');
  const exists = fs.existsSync(gitDir);
  const status = exists ? '✅' : '❌';
  const color = exists ? 'green' : 'red';
  log(`${status} ${name} Git仓库`, color);
  return exists;
}

function getPackageInfo(packagePath) {
  try {
    const content = fs.readFileSync(packagePath, 'utf8');
    const pkg = JSON.parse(content);
    return {
      name: pkg.name || 'Unknown',
      version: pkg.version || '0.0.0',
      description: pkg.description || 'No description'
    };
  } catch (error) {
    return null;
  }
}

function main() {
  log('\n' + '='.repeat(60), 'cyan');
  log('K12教育资源平台 - 项目状态检查', 'bright');
  log('='.repeat(60), 'cyan');

  // 检查项目结构
  log('\n📁 项目结构检查:', 'bright');
  checkExists('.', '项目根目录');
  checkExists('k12-wx', 'K12小程序目录');
  checkExists('k12-admin', '管理后台目录');
  checkExists('k12-dev-tool', '开发工具目录');

  // 检查Git仓库
  log('\n🔄 Git仓库检查:', 'bright');
  checkGitRepo('k12-wx', 'K12小程序');
  checkGitRepo('k12-admin', '管理后台');
  checkGitRepo('k12-dev-tool', '开发工具');

  // 检查配置文件
  log('\n⚙️ 配置文件检查:', 'bright');
  checkExists('package.json', '根目录 package.json');
  checkExists('README.md', '根目录 README.md');
  checkExists('.gitignore', '根目录 .gitignore');
  checkExists('k12-workspace.code-workspace', 'VSCode工作区配置');

  // 检查小程序文件
  log('\n📱 小程序文件检查:', 'bright');
  checkExists('k12-wx/app.js', '小程序入口文件');
  checkExists('k12-wx/app.json', '小程序配置文件');
  checkExists('k12-wx/project.config.json', '小程序项目配置');
  checkExists('k12-wx/sitemap.json', '小程序站点地图');

  // 检查管理后台文件
  log('\n🖥️ 管理后台文件检查:', 'bright');
  checkExists('k12-admin/README.md', '管理后台说明文档');
  checkExists('k12-admin/package.json', '管理后台 package.json');
  checkExists('k12-admin/开发需求文档.md', '开发需求文档');
  
  // 前端文件
  checkExists('k12-admin/frontend', '前端目录');
  checkExists('k12-admin/frontend/package.json', '前端 package.json');
  checkExists('k12-admin/frontend/vite.config.js', 'Vite配置文件');
  checkExists('k12-admin/frontend/.env.example', '前端环境配置模板');
  
  // 后端文件
  checkExists('k12-admin/backend', '后端目录');
  checkExists('k12-admin/backend/package.json', '后端 package.json');
  checkExists('k12-admin/backend/.env.example', '后端环境配置模板');

  // 检查开发工具文件
  log('\n🔧 开发工具文件检查:', 'bright');
  checkExists('k12-dev-tool/README.md', '开发工具说明文档');
  checkExists('k12-dev-tool/package.json', '开发工具 package.json');
  checkExists('k12-dev-tool/docs', '文档目录');
  checkExists('k12-dev-tool/scripts', '脚本目录');
  checkExists('k12-dev-tool/tools', '工具目录');

  // 检查依赖安装状态
  log('\n📦 依赖安装状态:', 'bright');
  checkExists('node_modules', '根目录依赖');
  checkExists('k12-admin/node_modules', '管理后台根依赖');
  checkExists('k12-admin/frontend/node_modules', '前端依赖');
  checkExists('k12-admin/backend/node_modules', '后端依赖');
  checkExists('k12-dev-tool/node_modules', '开发工具依赖');

  // 显示包信息
  log('\n📋 包信息:', 'bright');
  
  const packages = [
    { path: 'package.json', name: '根项目' },
    { path: 'k12-admin/package.json', name: '管理后台' },
    { path: 'k12-admin/frontend/package.json', name: '前端' },
    { path: 'k12-admin/backend/package.json', name: '后端' },
    { path: 'k12-dev-tool/package.json', name: '开发工具' }
  ];

  packages.forEach(({ path: pkgPath, name }) => {
    if (fs.existsSync(pkgPath)) {
      const info = getPackageInfo(pkgPath);
      if (info) {
        log(`  ${name}: ${info.name}@${info.version}`, 'blue');
      }
    }
  });

  // 检查环境配置
  log('\n🌍 环境配置检查:', 'bright');
  const envExists = checkExists('k12-admin/backend/.env', '后端环境配置');
  if (!envExists) {
    log('  💡 提示: 请复制 .env.example 为 .env 并配置参数', 'yellow');
  }

  // 总结
  log('\n📊 检查完成!', 'bright');
  log('如果发现问题，请参考各项目的README文档进行修复。', 'cyan');
  
  // 快速启动提示
  log('\n🚀 快速启动命令:', 'bright');
  log('  node start.js install    # 安装所有依赖', 'green');
  log('  node start.js backend    # 启动后端服务', 'green');
  log('  node start.js frontend   # 启动前端服务', 'green');
  log('  node start.js help       # 查看更多命令', 'green');
}

main();
