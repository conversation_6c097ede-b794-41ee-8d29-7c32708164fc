{"name": "k12-admin-backend", "version": "1.0.0", "description": "K12教育资源管理后台 - 后端服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "node test-permissions.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "joi": "^17.11.0", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["k12", "education", "admin", "cloudbase"], "author": "K12 Admin Team", "license": "MIT"}